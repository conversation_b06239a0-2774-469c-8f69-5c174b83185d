import 'dart:async';
import 'dart:io';
import 'package:ddone/app.dart';

import 'package:ddone/init.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/utils/screen_util.dart';

import 'package:ddone/utils/logger_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:launch_at_startup/launch_at_startup.dart';
import 'package:get_it/get_it.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_store_plus/media_store_plus.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:moxxmpp_socket_tcp/moxxmpp_socket_tcp.dart';
import 'package:screen_retriever/screen_retriever.dart';

import 'package:window_manager/window_manager.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

/// Sets initial device orientations (portrait only as fallback)
/// The proper orientations will be set later in app initialization when BuildContext is available
Future<void> _setDeviceOrientations() async {
  // Set portrait only initially for all platforms
  // This will be updated in app.dart once we have BuildContext to detect Android tablets
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
}

Future<void> main() async {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    MediaKit.ensureInitialized();

    await FirebaseService.initializeApp();

    await initializeLogger();

    await mainInit();

    if (isMobile) {
      if (isAndroid) MediaStore.ensureInitialized();
      MediaStore.appFolder = 'DDOne';
    }

    if (isDesktop) {
      launchAtStartup.setup(
        appName: 'DDOne',
        appPath: Platform.resolvedExecutable,
        // Set packageName parameter to support MSIX.
        packageName: 'com.dotdashtech.ddone',
      );
    }

    final BookmarkListCubit bookmarkListCubit = BookmarkListCubit();

    GetIt.I.registerSingleton<XmppConnection>(
      XmppConnection(
        RandomBackoffReconnectionPolicy(1, 60),
        AlwaysConnectedConnectivityManager(),
        ClientToServerNegotiator(),
        // The below causes the app to crash.
        //ExampleTcpSocketWrapper(),
        // In a production app, the below should be false.
        TCPSocketWrapper(false),
        bookmarkListCubit: bookmarkListCubit,
      ),
    );
    //fluttrt allow self sign cert - no good, remove after getting a valid cert
    HttpOverrides.global = MyHttpOverrides();

    EasyLoadingService().initEasyLoadingService();

    if (isDesktop) {
      Display primaryScreen = await screenRetriever.getPrimaryDisplay();
      double appWidth = primaryScreen.size.width * 0.6;
      double appHeight = primaryScreen.size.height * 0.8;

      await windowManager.ensureInitialized();

      WindowManager.instance.setSize(Size(appWidth, appHeight));
      WindowManager.instance.setResizable(true);
      WindowManager.instance.setMaximizable(true);
      WindowManager.instance.setMinimumSize(Size(appWidth, appHeight));
      // WindowManager.instance.setMaximumSize(Size(appWidth, appHeight));
    }

    await _setDeviceOrientations();
    runApp(const DDOneApp());
  }, (error, stackTrace) {
    log.f('runZonedGuarded', error: error, stackTrace: stackTrace);
  });
}
