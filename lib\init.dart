import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/hive/favourite_contact.dart';
import 'package:ddone/repositories/endpointmgt_repository.dart';
import 'package:ddone/repositories/fcmtoken_repository.dart';
import 'package:ddone/services/audio_device_service.dart';
import 'package:ddone/services/audio_session_service.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/download_file_service.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/janus_service.dart';
import 'package:ddone/services/media_service.dart';
import 'package:ddone/services/multiplatform/multiplatform.dart';
import 'package:ddone/repositories/auth_repository.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/native/notification_native_service.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/services/ntp_datetime_service.dart';
import 'package:ddone/services/shared_preferences_service.dart';
import 'package:ddone/services/xmpp/xmpp_service.dart';
import 'package:ddone/utils/app_version_util.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/models/hive/local_contact.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:dio/dio.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:logger/logger.dart';
import 'package:ntp/ntp.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';
import 'package:uuid/uuid.dart';
import 'package:windows_notification/windows_notification.dart';
import 'package:ddone/models/enums/platform_enum.dart';
import 'package:event_bus_plus/event_bus_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

Future<void> registerDependencies() async {
  if (!sl.isRegistered<SharedPreferences>()) {
    final sharedPref = await SharedPreferences.getInstance();
    sl.registerLazySingleton<SharedPreferences>(() => sharedPref);
  }

  final sharedPreferencesService = await SharedPreferencesService.getInstance();
  if (!sl.isRegistered<SharedPreferencesService>()) {
    sl.registerLazySingleton<SharedPreferencesService>(() => sharedPreferencesService);
  }

  if (!sl.isRegistered<Dio>()) {
    final dio = Dio()
      ..options = BaseOptions(baseUrl: env!.apiUrl)
      ..interceptors.addAll([]);
    sl.registerLazySingleton<Dio>(() => dio);
  }

  if (!sl.isRegistered<FlutterSecureStorage>()) {
    sl.registerLazySingleton<FlutterSecureStorage>(() => const FlutterSecureStorage());
  }

  if (!sl.isRegistered<Logger>()) {
    sl.registerLazySingleton<Logger>(() => log);
  }

  if (!sl.isRegistered<StopWatchTimer>()) {
    sl.registerLazySingleton<StopWatchTimer>(() => StopWatchTimer(mode: StopWatchMode.countUp));
  }

  if (!sl.isRegistered<InternetConnectionChecker>()) {
    sl.registerLazySingleton<InternetConnectionChecker>(() => InternetConnectionChecker());
  }

  if (!sl.isRegistered<Connectivity>()) {
    sl.registerLazySingleton<Connectivity>(() => Connectivity());
  }

  if (!sl.isRegistered<TimeService>()) {
    try {
      final DateTime ntp = await NTP.now();
      sl.registerLazySingleton<TimeService>(() => TimeService(ntp));
    } catch (_) {
      sl.registerLazySingleton<TimeService>(() => TimeService(DateTime.now()));
    }
  }

  if (!sl.isRegistered<HiveService>()) {
    sl.registerLazySingleton<HiveService>(() => HiveService());
  }

  if (!sl.isRegistered<Uuid>()) {
    sl.registerLazySingleton<Uuid>(() => const Uuid());
  }

  if (!sl.isRegistered<FlutterLocalNotificationsPlugin>()) {
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    sl.registerLazySingleton<FlutterLocalNotificationsPlugin>(() => flutterLocalNotificationsPlugin);
  }

  if (!sl.isRegistered<NotificationService>()) {
    sl.registerLazySingleton<NotificationService>(() => NotificationService());
  }

  if (!sl.isRegistered<WindowsNotification>()) {
    final winNotifyPlugin = WindowsNotification(applicationId: null);
    sl.registerLazySingleton<WindowsNotification>(() => winNotifyPlugin);
  }

  if (!sl.isRegistered<NotificationNativeService>()) {
    sl.registerLazySingleton(() => NotificationNativeService());
  }

  if (!sl.isRegistered<PlatformEnum>()) {
    sl.registerLazySingleton<PlatformEnum>(() => getPlatform());
  }

  if (!sl.isRegistered<JanusService>()) {
    sl.registerLazySingleton<JanusService>(() => JanusService());
  }

  if (!sl.isRegistered<MediaService>()) {
    sl.registerLazySingleton<MediaService>(() => MediaService());
  }

  if (!sl.isRegistered<AudioSessionService>()) {
    sl.registerLazySingleton<AudioSessionService>(() => AudioSessionService.instance);
  }

  if (!sl.isRegistered<AudioDeviceService>()) {
    sl.registerLazySingleton<AudioDeviceService>(() => AudioDeviceService());
  }

  if (!sl.isRegistered<IEventBus>()) {
    final IEventBus eventBus = EventBus();
    sl.registerLazySingleton<IEventBus>(() => eventBus);
  }

  if (!sl.isRegistered<FirebaseService>()) {
    sl.registerLazySingleton<FirebaseService>(() => FirebaseService());
  }

  if (!sl.isRegistered<CallkitService>()) {
    sl.registerLazySingleton<CallkitService>(() => CallkitService());
  }

  if (!sl.isRegistered<XmppService>()) {
    sl.registerLazySingleton<XmppService>(() => XmppService());
  }

  if (!sl.isRegistered<DownloadFileService>()) {
    sl.registerLazySingleton<DownloadFileService>(() => DownloadFileService());
  }
}

Future<void> registerHiveServices() async {
  Directory localStorageDir = await getApplicationCacheDirectory();
  Hive.init(localStorageDir.path);
  if (!Hive.isAdapterRegistered(CallRecordsAdapter().hashCode)) {
    Hive.registerAdapter(CallRecordsAdapter());
    Hive.registerAdapter(LocalContactAdapter());
    Hive.registerAdapter(CallTypeAdapter());
    Hive.registerAdapter(FavouriteContactAdapter());
  }
  final hiveService = sl.get<HiveService>();
  hiveService.initialHiveBox();
}

Future<void> initEnv() async {
  final sharedPreferencesService = await SharedPreferencesService.getInstance();
  String flavorString;
  if (BuildEnvironment.appFlavor == null) {
    flavorString = sharedPreferencesService.getString(CacheKeys.buildFlavor) ??
        EnumToString.convertToString(BuildFlavor.development);
  } else {
    flavorString = EnumToString.convertToString(BuildEnvironment.appFlavor);
  }

  await dotenv.load(fileName: 'env/.env.$flavorString');

  String appVer = await getReleaseVersion(() => rootBundle.loadString('pubspec.yaml'));

  BuildEnvironment.init(
    flavor: EnumToString.fromString(BuildFlavor.values, flavorString) ?? BuildFlavor.development,
    appName: dotenv.get('APP_NAME', fallback: ''),
    apiUrl: dotenv.get('API_URL', fallback: ''),
    webSocketUrl: dotenv.get('WEB_SOCKET_URL', fallback: ''),
    pnUrl: dotenv.get('PN_URL', fallback: ''),
    minioUrl: dotenv.get('MINIO_URL', fallback: ''),
    audioSource: dotenv.get('AUDIO_SOURCE', fallback: ''),
    incomingCallAudioSource: dotenv.get('IN_COMING_CALL_AUDIO_SOURCE', fallback: ''),
    contactUsUrl: dotenv.get('CONTACT_US_URL', fallback: ''),
    appVersion: appVer.split('+').first,
    appBuildNumber: appVer.split('+').last,
    xmppAccount: dotenv.get('XMPP_ACCOUNT', fallback: ''),
    xmppAccountSec: dotenv.get('XMPP_ACCOUNT_SEC', fallback: ''),
    mongooseimXmppHost: dotenv.get('MONGOOSEIM_XMPP_HOST', fallback: ''),
    mongooseimXmppPort: dotenv.get('MONGOOSEIM_XMPP_PORT', fallback: ''),
    endpointmgtUrl: dotenv.get('ENDPOINTMGT_URL', fallback: ''),
    checkAppUpdatePath: dotenv.get('CHECK_APP_UPDATE_PATH', fallback: ''),
    updateUserPath: dotenv.get('UPDATE_USER_PATH', fallback: ''),
    storeCallHistoryPath: dotenv.get('STORE_CALL_HISTORY_PATH', fallback: ''),
    retrieveCallHistoryPath: dotenv.get('RETRIEVE_CALL_HISTORY_PATH', fallback: ''),
    userGuideUrl: dotenv.get('USER_GUIDE_URL', fallback: ''),
  );

  sharedPreferencesService.setString(CacheKeys.buildFlavor, flavorString);
}

Future<void> registerRepo() async {
  final dio = sl.get<Dio>();
  if (!sl.isRegistered<AuthRepository>()) {
    sl.registerLazySingleton<AuthRepository>(() => AuthRepository(dio));
  }
  if (!sl.isRegistered<FcmtokenRepository>()) {
    sl.registerLazySingleton<FcmtokenRepository>(() => FcmtokenRepository(dio, baseUrl: env!.pnUrl));
  }
  if (!sl.isRegistered<EndpointmgtRepository>()) {
    sl.registerLazySingleton<EndpointmgtRepository>(() => EndpointmgtRepository(dio, baseUrl: env!.endpointmgtUrl));
  }
}

Future<void> logPackageInfo() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  log.i(
      'PACKAGE INFO:  appName:${packageInfo.appName} packageName:${packageInfo.packageName} version:${packageInfo.version} buildNumber:${packageInfo.buildNumber}');
}

Future<void> mainInit() async {
  await initEnv(); // Awalys first
  await registerDependencies(); // Always second
  await Future.wait([
    registerRepo(),
    registerHiveServices(),
  ]);
  logPackageInfo();
}
