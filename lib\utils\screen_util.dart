import 'package:device_info_plus/device_info_plus.dart';
import 'package:ddone/models/enums/platform_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/multiplatform/multiplatform.dart';
import 'package:flutter/material.dart';

bool get isAndroid {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.android == platform;
}

bool get isIOS {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.ios == platform;
}

bool get isMobile {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return {
    PlatformEnum.android,
    PlatformEnum.ios,
  }.contains(platform);
}

bool get isMacOS {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.macos == platform;
}

bool get isWindows {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.windows == platform;
}

bool get isDesktop {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return {
    PlatformEnum.web,
    PlatformEnum.macos,
    PlatformEnum.windows,
    PlatformEnum.linux,
  }.contains(platform);
}

/// Detects if the current device is an iPad
Future<bool> get isIpad async {
  if (!isIOS) return false;

  try {
    final deviceInfo = DeviceInfoPlugin();
    final iosInfo = await deviceInfo.iosInfo;

    // Check if device model contains 'iPad'
    return iosInfo.model.toLowerCase().contains('ipad');
  } catch (e) {
    return false;
  }
}

/// Detects if the current device is an Android tablet
bool isAndroidTablet(BuildContext context) {
  if (!isAndroid) return false;

  try {
    // Get screen metrics
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    // Android tablet detection based on:
    // 1. Smallest width >= 600dp (Android's official tablet threshold)
    // 2. OR screen width >= 600dp (for landscape orientation)
    final smallestWidth = screenWidth < screenHeight ? screenWidth : screenHeight;

    return smallestWidth >= 600 || screenWidth >= 600;
  } catch (e) {
    // Fallback: Use responsive breakpoint detection
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;

    // Consider it a tablet if width is in tablet range (451px+) based on app's breakpoints
    return screenWidth >= 451;
  }
}

/// Detects if the current device is a tablet (iPad or Android tablet)
Future<bool> isTablet(BuildContext context) async {
  // For iOS, check if it's an iPad
  if (isIOS) {
    return await isIpad;
  }

  // For Android, check if it's a tablet using screen metrics
  if (isAndroid) {
    return isAndroidTablet(context);
  }

  // For other platforms, not considered tablets
  return false;
}
