import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/events/network_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/enums/janus_sip_event_enum.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/services/audio_device_service.dart';
import 'package:ddone/services/audio_session_service.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:equatable/equatable.dart';
import 'package:event_bus_plus/event_bus_plus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:logger/logger.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/janus_service.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

part 'janus_state.dart';

class JanusCubit extends Cubit<JanusState> with PrefsAware {
  late final Logger log;
  late final JanusService janusService;
  late final HiveService hiveService;
  late final NotificationService notificationService;
  late final IEventBus eventBus;
  late final CallkitService callkitService;
  late final FirebaseService firebaseService;
  late final AudioSessionService audioSessionService;
  late final AudioDeviceService audioDeviceService;

  bool _initializing = false;
  bool _reconnecting = false;
  bool _acceptingCall = false;
  bool _acceptedCall = false;
  Timer? _acceptedCallCheckTimer;

  String? _caller;
  String? _callerId;

  // store args for init
  String? _sipWsUrl;
  Future<void> Function()? _sipRegisteredEventCallback;
  Future<void> Function(String, String)? _sipIncomingCallEventCallback;
  Future<void> Function()? _sipAcceptedEventCallback;
  Future<void> Function()? _sipProgressEventCallback;
  Future<void> Function()? _sipCallingEventCallback;
  Future<void> Function()? _sipProceedingEventCallback;
  Future<void> Function()? _sipRingingEventCallback;
  Future<void> Function()? _sipHangupEventCallback;
  Future<void> Function()? _sipUnRegisteredEventCallback;
  Future<void> Function()? _sipTransferCallEventCallback;
  Future<void> Function()? _sipMissedCallEventCallback;
  Future<void> Function(dynamic)? _sipErrorCallback;

  // store args for register
  String? _sipNumber;
  String? _sipDomain;
  String? _sipProxy;
  String? _sipSecret;
  String? _sipName;

  JanusCubit._({
    JanusState? state,
  })  : log = sl.get<Logger>(),
        janusService = sl.get<JanusService>(),
        hiveService = sl.get<HiveService>(),
        notificationService = sl.get<NotificationService>(),
        eventBus = sl.get<IEventBus>(),
        callkitService = sl.get<CallkitService>(),
        firebaseService = sl.get<FirebaseService>(),
        audioSessionService = sl.get<AudioSessionService>(),
        audioDeviceService = sl.get<AudioDeviceService>(),
        super(state ?? const JanusInitial()) {
    eventBus.on<NetworkEvent>().listen((event) {
      if (event.state is NetworkReconnected) {
        log.t('janusCubit - eventBus: networkEvent:$event');
        maintainConnection();
      } else if (event.state is NetworkDisconnected) {
        hangupCallWhenNetworkDisconnect();
      }
    });
  }

  factory JanusCubit.initial({JanusState? state}) {
    return sl.isRegistered<JanusCubit>() ? sl.get<JanusCubit>() : JanusCubit._(state: state);
  }

  RTCVideoRenderer get remoteVideoRenderer => janusService.remoteVideoRenderer;

  void emitJanusState(Type stateType, {dynamic params}) {
    if (stateType == JanusConnectionError) {
      assert(params is String, 'Params of JanusConnectionError must be String');
    } else if (stateType == JanusRegisterError) {
      assert(params is String, 'Params of JanusRegisterError must be String');
    } else if (stateType == JanusSipIncomingCallEvent) {
      assert(params is List<String> && params.length == 3,
          'Params of JanusSipIncomingCallEvent must be a List of 3 Strings');
    } else if (stateType == JanusSipHangupEvent) {
      assert(params is String, 'Params of JanusSipHangupEvent must be String');
    } else if (stateType == JanusSipProceedingEvent) {
      assert(
          params is List<String> && params.length == 2, 'Params of JanusSipProceedingEvent must be List of 2 String');
    } else if (stateType == JanusSipCallingEvent) {
      assert(params is List<String> && params.length == 2, 'Params of JanusSipCallingEvent must be List of 2 String');
    } else if (stateType == JanusSipProgressEvent) {
      assert(params is List<String> && params.length == 2, 'Params of JanusSipProgressEvent must be List of 2 String');
    } else if (stateType == JanusSipAcceptedEvent) {
      assert(params is List && params.length == 3, 'Params of JanusSipAcceptedEvent must be List of 3 String');
    }

    final stateMap = {
      JanusInitial: () => const JanusInitial(),
      JanusSipRegisteredEvent: () => const JanusSipRegisteredEvent(),
      JanusSipUnregisteredEvent: () => const JanusSipUnregisteredEvent(),
      JanusSipIncomingCallEvent: () => JanusSipIncomingCallEvent(
            callerID: params[0],
            caller: params[1],
            statusMessage: params[2],
          ),
      JanusSipAcceptedEvent: () => JanusSipAcceptedEvent(
            statusMessage: params[0],
            callerID: params[1],
            caller: params[2],
          ),
      JanusSipProgressEvent: () => JanusSipProgressEvent(
            statusMessage: params[0],
            callTo: params[1],
          ),
      JanusSipCallingEvent: () => JanusSipCallingEvent(
            statusMessage: params[0],
            callTo: params[1],
          ),
      JanusSipTransferCallEvent: () => JanusSipTransferCallEvent(statusMessage: params),
      JanusSipProceedingEvent: () => JanusSipProceedingEvent(
            statusMessage: params[0],
            callTo: params[1],
          ),
      JanusSipRingingEvent: () => JanusSipRingingEvent(statusMessage: params),
      JanusSipHangupEvent: () => JanusSipHangupEvent(statusMessage: params),
      JanusSipHangupErrorEvent: () => const JanusSipHangupErrorEvent(),
      JanusSipAcceptErrorEvent: () => const JanusSipAcceptErrorEvent(),
      JanusRegisterError: () => JanusRegisterError(errorMsg: params),
      JanusConnectionError: () => JanusConnectionError(errorMsg: params),
    };

    emit(stateMap[stateType]?.call() ?? const JanusInitial());
  }

  Future<bool> init({
    required String? sipWsUrl,
    Future<void> Function()? sipRegisteredEventCallback,
    Future<void> Function(String, String)? sipIncomingCallEventCallback,
    Future<void> Function()? sipAcceptedEventCallback,
    Future<void> Function()? sipProgressEventCallback,
    Future<void> Function()? sipCallingEventCallback,
    Future<void> Function()? sipProceedingEventCallback,
    Future<void> Function()? sipRingingEventCallback,
    Future<void> Function()? sipHangupEventCallback,
    Future<void> Function()? sipUnRegisteredEventCallback,
    Future<void> Function()? sipTransferCallEventCallback,
    Future<void> Function()? sipMissedCallEventCallback,
    Future<void> Function(dynamic)? sipErrorCallback,
  }) async {
    _initializing = true;
    try {
      bool hasInit = await janusService.init(sipWsUrl);
      if (!hasInit) return false;
    } catch (e) {
      emitJanusState(
        JanusRegisterError,
        params: e.toString(),
      );
      return false;
    }

    await janusService.initMedia();

    janusService.listenSipEvent(
      sipRegisteredEventCallback: () async {
        // when ttl between fusion and janus expired, janus will automatically re-register.
        if (state is JanusSipIncomingCallEvent ||
            state is JanusSipAcceptedEvent ||
            state is JanusSipProgressEvent ||
            state is JanusSipCallingEvent ||
            state is JanusSipProceedingEvent ||
            state is JanusSipRingingEvent ||
            state is JanusSipTransferCallEvent) return;
        emitJanusState(JanusSipRegisteredEvent);
        _caller = null;
        _callerId = null;
        startAcceptedCallCheckTimer();
        if (sipRegisteredEventCallback != null) await sipRegisteredEventCallback();
      },
      sipIncomingCallEventCallback: (callerId, caller) async {
        callkitService.setActiveCallUuid();
        if (isIOS) {
          // wait for callkit to ring first, otheriwse audio session will messup when user accept call before it rings
          await waitForCondition(() async => audioSessionService.interrupted,
              timeout: const Duration(milliseconds: 1500));
        }
        _caller = caller;
        _callerId = callerId;
        emitJanusState(JanusSipIncomingCallEvent, params: [
          callerId,
          caller,
          '',
        ]);
        prefs.remove(CacheKeys.callkitStartCallTime);
        if (sipIncomingCallEventCallback != null) await sipIncomingCallEventCallback(callerId, caller);
      },
      sipAcceptedEventCallback: () async {
        _acceptedCall = true;
        callkitService.hideNotificationCall();
        configureDefaultAudioDevice();
        audioDeviceService.startDeviceMonitoring();
        _preventScreenOff(true);
        emitJanusState(
          JanusSipAcceptedEvent,
          params: [
            JanusSipEvent.accepted.statusMessage(),
            _callerId,
            _caller,
          ],
        );
        callkitService.connectedCall();
        if (sipAcceptedEventCallback != null) await sipAcceptedEventCallback();
      },
      sipProgressEventCallback: () async {
        emitJanusState(
          JanusSipProgressEvent,
          params: [
            JanusSipEvent.progress.statusMessage(),
            state.callTo,
          ],
        );
        if (sipProgressEventCallback != null) await sipProgressEventCallback();
      },
      sipCallingEventCallback: () async {
        emitJanusState(
          JanusSipCallingEvent,
          params: [
            JanusSipEvent.calling.statusMessage(),
            state.callTo,
          ],
        );
        prefs.remove(CacheKeys.callkitStartCallTime);
        if (sipCallingEventCallback != null) await sipCallingEventCallback();
      },
      sipProceedingEventCallback: () async {
        emitJanusState(
          JanusSipProceedingEvent,
          params: [
            JanusSipEvent.proceeding.statusMessage(),
            state.callTo,
          ],
        );
        if (sipProgressEventCallback != null) await sipProgressEventCallback();
      },
      sipRingingEventCallback: () async {
        emitJanusState(JanusSipRingingEvent, params: '');
        if (sipRingingEventCallback != null) await sipRingingEventCallback();
      },
      sipHangupEventCallback: () async {
        callkitService.endCall();
        restoreSpeaker();
        audioDeviceService.stopDeviceMonitoring();
        _preventScreenOff(false);
        _acceptedCall = false;
        _callerId = null;
        _caller = null;

        // Wait a bit before dispose. Sometimes there are something still need to be
        // written to websocket or something duno what after hang up. Let it finish before dispose.
        await Future.delayed(const Duration(milliseconds: 500));
        await dispose();
        audioSessionService.resetSession();
        emitJanusState(
          JanusSipHangupEvent,
          params: JanusSipEvent.hangup.statusMessage(),
        );
        if (await isAppOpen()) {
          bool hasReinit = await reinitialize();
          if (hasReinit) await reregister();
        }
        prefs.remove(CacheKeys.callkitStartCallTime);
        if (sipHangupEventCallback != null) await sipHangupEventCallback();
      },
      sipUnRegisteredEventCallback: () async {
        emitJanusState(JanusSipUnregisteredEvent);
        if (sipUnRegisteredEventCallback != null) await sipUnRegisteredEventCallback();
      },
      sipTransferCallEventCallback: () async {
        emitJanusState(JanusSipTransferCallEvent);
        if (sipTransferCallEventCallback != null) await sipTransferCallEventCallback();
      },
      sipMissedCallEventCallback: () async {
        if (sipMissedCallEventCallback != null) await sipMissedCallEventCallback();
      },
      sipErrorCallback: (error) async {
        emitJanusState(
          JanusConnectionError,
          params: error.error.toString(),
        );
        if (sipErrorCallback != null) await sipErrorCallback(error);
      },
    );

    // Store the args so later reinitialize can use
    _sipWsUrl = sipWsUrl;
    _sipRegisteredEventCallback = sipRegisteredEventCallback;
    _sipIncomingCallEventCallback = sipIncomingCallEventCallback;
    _sipAcceptedEventCallback = sipAcceptedEventCallback;
    _sipProgressEventCallback = sipProgressEventCallback;
    _sipCallingEventCallback = sipCallingEventCallback;
    _sipProceedingEventCallback = sipProceedingEventCallback;
    _sipRingingEventCallback = sipRingingEventCallback;
    _sipHangupEventCallback = sipHangupEventCallback;
    _sipUnRegisteredEventCallback = sipUnRegisteredEventCallback;
    _sipTransferCallEventCallback = sipTransferCallEventCallback;
    _sipMissedCallEventCallback = sipMissedCallEventCallback;
    _sipErrorCallback = sipErrorCallback;

    _initializing = false;
    return true;
  }

  Future<bool> reinitialize() async {
    if (_sipWsUrl == null) return false;
    return init(
      sipWsUrl: _sipWsUrl,
      sipRegisteredEventCallback: _sipRegisteredEventCallback,
      sipIncomingCallEventCallback: _sipIncomingCallEventCallback,
      sipAcceptedEventCallback: _sipAcceptedEventCallback,
      sipProgressEventCallback: _sipProgressEventCallback,
      sipCallingEventCallback: _sipCallingEventCallback,
      sipProceedingEventCallback: _sipProceedingEventCallback,
      sipRingingEventCallback: _sipRingingEventCallback,
      sipHangupEventCallback: _sipHangupEventCallback,
      sipUnRegisteredEventCallback: _sipUnRegisteredEventCallback,
      sipTransferCallEventCallback: _sipTransferCallEventCallback,
      sipMissedCallEventCallback: _sipMissedCallEventCallback,
      sipErrorCallback: _sipErrorCallback,
    );
  }

  Future<bool> isConnected() async {
    return janusService.checkJanusConnection();
  }

  Future<void> maintainConnection() async {
    if (!userHasLoggedIn()) return;
    if (_reconnecting || _initializing) return;
    _reconnecting = true;
    try {
      bool janusIsConnected = await isConnected();
      log.t('JanusCubit - maintainConnection - janusIsConnected:$janusIsConnected');
      if (!janusIsConnected) {
        await dispose();
        emitJanusState(JanusSipUnregisteredEvent);
        bool hasReinit = await reinitialize();
        if (hasReinit) await reregister();
      }
    } finally {
      // wait a while then only release the flag, this is to ensure Janus server side has registered
      // before disposing it again.
      await Future.delayed(const Duration(milliseconds: 500));
      _reconnecting = false;
    }
  }

  Future<bool> waitUntilConnected() => janusService.waitJanusConnect();

  Future<void> dispose() async {
    _acceptedCall = false;
    await janusService.dispose();
  }

  Future<void> disposeWhenIsMobileAndNoCalls() async {
    if (!isMobile) return;
    bool hasActiveCalls = await callkitService.hasActiveCalls();
    if (hasActiveCalls) {
      await Future.delayed(const Duration(milliseconds: 3000));
      JanusCallStatus janusCallStatus = await janusService.checkJanusCallStatus();
      if (janusCallStatus == JanusCallStatus.nocall) {
        log.t('disposeWhenIsMobileAndNoCalls - callkit has call - janus no call - encallkit - dispose');
        callkitService.endCall();
        dispose();
      }
    } else {
      log.t('disposeWhenIsMobileAndNoCalls - callkit no call - dispose');
      dispose();
    }
  }

  Future<void> register(
      {required String sipNumber,
      required String sipDomain,
      required String sipProxy,
      required String sipSecret,
      String? sipName}) async {
    if (state is JanusSipRegisteredEvent) return;
    try {
      String? token;
      if (isMobile) {
        token = await firebaseService.getFcmtoken();
      } else {
        token = 'desktop';
      }
      // bool isRegistered = await janusService.isRegisteredInFusion(sipDomain, sipNumber, token!);
      // if (isRegistered) {
      // can i ping fusion side to unregister myself? if can, then means i can try to delete those hanging session and start from fresh.
      // otherwise when there is hanging session, we can't do anything either.
      // also, need to check whether there is ongoing call. if there is, then:
      // 1) immediately close the 2nd app (if it does not affect the ongoing call)
      // 2) do not register and inform the 2nd app to show the accepted call screen, after the call has ended, the 2nd app should then close itself.
      // 3) do not register and inform the 2nd app to show the accepted call screen, after the call has ended, close the 1st app and register in the 2nd app.
      // log.w(
      // 'Janus session for domain:$sipDomain extension:$sipNumber token:$token already exist. Stuff may mess up!');
      // }
      await janusService.register('sip:$sipNumber@$sipDomain', 'sip:$sipProxy', sipSecret, sipName, token);

      // Store the args so later reregister can use
      _sipNumber = sipNumber;
      _sipDomain = sipDomain;
      _sipProxy = sipProxy;
      _sipSecret = sipSecret;
      _sipName = sipName;
    } catch (e) {
      log.e('Failed to register janus', error: e);
      emitJanusState(
        JanusRegisterError,
        params: '$e | username - $sipNumber@$sipDomain | proxy - sip:$sipProxy | secret - $sipSecret',
      );
    }
  }

  Future<void> reregister() async {
    if (_sipNumber == null || _sipDomain == null || _sipSecret == null || _sipName == null) return;
    await register(
        sipNumber: _sipNumber!,
        sipDomain: _sipDomain!,
        sipProxy: _sipProxy!,
        sipSecret: _sipSecret!,
        sipName: _sipName);
  }

  Future<void> makeCall(String extNum, String sipDomain, String ctcName) async {
    try {
      if (isAndroid) {
        // only android need this, iOS audio session is handled by callkit, windows no need care.
        await audioSessionService.activateCallSession();
      }
      await janusService.initLocalStream();
      janusService.makeCall(extNum, sipDomain);

      emitJanusState(
        JanusSipCallingEvent,
        params: [
          JanusSipEvent.proceeding.statusMessage(),
          ctcName,
        ],
      );

      hiveService.addData<CallRecords>(
        data: CallRecords(
          contactName: ctcName,
          did: extNum,
          duration: '0:00',
          type: CallType.outgoing,
          datetime: DateTime.now(),
        ),
      );
      callkitService.outgoingCall(ctcName, extNum);
      _callerId = extNum;
      _caller = ctcName;
    } catch (e) {
      log.e('Failed to make call', error: e);
      audioSessionService.resetSession();
      emitJanusState(
        JanusConnectionError,
        params: e.toString(),
      );
      _callerId = null;
      _caller = null;
    }
  }

  Future<void> acceptCall() async {
    if (_acceptingCall || _acceptedCall) return;
    try {
      _acceptingCall = true;
      if (isAndroid) {
        // only android need this, iOS audio session is handled by callkit, windows no need care.
        await audioSessionService.activateCallSession();
      }
      await janusService.initLocalStream();
      await janusService.acceptCall();
      callkitService.acceptedCall = true;
      _acceptedCall = true;
    } catch (e) {
      log.e('Failed to accept call', error: e);
      emitJanusState(JanusSipAcceptErrorEvent);
      callkitService.endCall();
      audioSessionService.resetSession();
      _acceptedCall = false;
    } finally {
      _acceptingCall = false;
    }
  }

  Future<void> transferCall(String extNum, String sipDomain) async {
    try {
      await janusService.transferCall(extNum, sipDomain);
    } catch (e) {
      log.e('Failed to transfer call', error: e);
    }
  }

  Future<void> declineCall() async {
    try {
      await janusService.declineCall();
    } catch (e) {
      log.e('Failed to decline call', error: e);
      callkitService.endCall();
      _callerId = null;
      _caller = null;
    }
  }

  Future<void> holdCall() async {
    await janusService.holdCall();
  }

  Future<void> unholdCall() async {
    await janusService.unholdCall();
  }

  Future<void> toggleHoldCall() async {
    await janusService.toggleHoldCall();
  }

  Future<void> hangupCall() async {
    try {
      await janusService.hangupCall();
    } catch (e) {
      log.e('Failed to hangup call', error: e);
      emitJanusState(JanusSipHangupErrorEvent);
      callkitService.endCall();
      _acceptedCall = false;
      _callerId = null;
      _caller = null;
    }
  }

  void muteMicAudio(bool mute) async {
    await janusService.muteMicAudio(mute);
  }

  void muteSpeakerAudio(bool mute) async {
    await janusService.muteSpeakerAudio(mute);
  }

  void configureDefaultAudioDevice() async {
    if (isAndroid) {
      // based on device priority, select the default audio device.
      final devices = await audioDeviceService.getAvailableDevices();
      final defaultDevice = audioDeviceService.getDefaultDevice(devices);
      if (defaultDevice != null) {
        audioDeviceService.selectDevice(defaultDevice);
        log.t('Selected to use ${defaultDevice.label} as default audio device');
      }
    }
  }

  void restoreSpeaker() async {
    if (isAndroid) {
      final devices = await audioDeviceService.getAvailableDevices();
      final loudspeakerDevice = devices.firstWhere(
        (d) => d.deviceId == audioDeviceService.getLoudSpeakerDeviceId(),
        orElse: () => devices.first,
      );
      audioDeviceService.selectDevice(loudspeakerDevice);
      log.t('Restored to use loud speaker after call');
    }
  }

  // NOT BEING USED ANYMORE, BUT LEAVE HERE FOR EASY FUTURE REFERENCE
  // void toggleLocalStream(bool enabled) {
  //   janusService.localStream?.getAudioTracks()[0].enabled = enabled;
  // }

  // void toggleLocalStreamSpeaker(bool enabled) {
  //   janusService.localStream?.getAudioTracks()[0].enableSpeakerphone(enabled);
  // }

  Future<void> checkCallOnHomeScreenStart() async {
    log.t('janusCubit - checkCallOnHomeScreenStart');
    if (isAndroid &&
        (state is JanusSipAcceptedEvent || state is JanusSipCallingEvent || _acceptingCall || _acceptedCall)) return;
    if (isIOS && state is JanusSipAcceptedEvent || state is JanusSipCallingEvent) return;
    if (isDesktop && state is JanusSipAcceptedEvent) return;
    JanusCallStatus janusCallStatus = await janusService.checkJanusCallStatus();
    log.t('janusCubit - checkCallOnHomeScreenStart - janusCallStatus:$janusCallStatus');
    if (janusCallStatus == JanusCallStatus.incall) {
      Map? callInfo = await callkitService.getCallExtraInfo();
      String callerId = '';
      String caller = '';
      if (callInfo != null) {
        callerId = callInfo['callerId'];
        caller = callInfo['caller'];
      }
      emitJanusState(JanusSipIncomingCallEvent, params: [
        callerId,
        caller,
        '',
      ]);
    }
  }

  Future<void> checkCallOnIncomingCallScreenStart() async {
    log.t('janusCubit - checkCallOnIncomingCallScreenStart');
    if (state is JanusSipAcceptedEvent) return;
    JanusCallStatus janusCallStatus = await janusService.checkJanusCallStatus();
    log.t('janusCubit - checkCallOnIncomingCallScreenStart - janusCallStatus:$janusCallStatus');
    if (janusCallStatus == JanusCallStatus.incall) {
      Map? callInfo = await callkitService.getCallExtraInfo();
      String callerId = '';
      String caller = '';
      if (callInfo != null) {
        callerId = callInfo['callerId'];
        caller = callInfo['caller'];
      }
      emitJanusState(JanusSipAcceptedEvent, params: [
        JanusSipEvent.accepted.statusMessage(),
        callerId,
        caller,
      ]);
    }
  }

  void startAcceptedCallCheckTimer() async {
    log.t('JanusCubit - startAcceptedCallCheckTimer');
    _acceptedCallCheckTimer?.cancel();
    _acceptedCallCheckTimer = Timer.periodic(const Duration(milliseconds: 1500), (_) => _acceptedCallCheck);
    await Future.delayed(const Duration(seconds: 10));
    stopAcceptedCallCheckTimer();
  }

  void stopAcceptedCallCheckTimer() {
    log.t('JanusCubit - stopAcceptedCallCheckTimer');
    _acceptedCallCheckTimer?.cancel();
    _acceptedCallCheckTimer = null;
  }

  void _acceptedCallCheck() {
    if (state is JanusSipAcceptedEvent) {
      stopAcceptedCallCheckTimer();
    } else {
      checkCallOnHomeScreenStart();
    }
  }

  void _preventScreenOff(bool enabled) {
    try {
      if (isAndroid) {
        WakelockPlus.toggle(enable: enabled);
      }
    } catch (e) {
      log.e('failed to toggle wakelock', error: e);
    }
  }

  void hangupCallWhenNetworkDisconnect() async {
    try {
      if (await callkitService.hasActiveCalls()) {
        await hangupCall();
        EasyLoadingService()
            .showErrorWithText('Call ended due to network disconnect.', duration: const Duration(seconds: 5));
        callkitService.endCall();
      }
    } catch (e) {
      log.e('Failed to hangup call when network disconnect', error: e);
    }
  }

  @override
  Future<void> close() async {
    await janusService.stopStreams();
    await janusService.disposeMedia();
    dispose();
    return super.close();
  }
}
