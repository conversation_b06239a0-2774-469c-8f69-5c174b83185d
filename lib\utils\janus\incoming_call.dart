import 'dart:async';

import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/home/<USER>/janus_cubit.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/background_service.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/janus/accepted_call.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class IncomingCallDialog extends StatefulWidget {
  final String? callerID, caller;
  final VoidCallback onAccept, onDecline;

  const IncomingCallDialog({
    required this.callerID,
    required this.caller,
    required this.onAccept,
    required this.onDecline,
    super.key,
  });

  @override
  State<IncomingCallDialog> createState() => _IncomingCallDialogState();
}

class _IncomingCallDialogState extends State<IncomingCallDialog> with WidgetsBindingObserver, PrefsAware {
  late HomeCubit _homeCubit;
  late JanusCubit _janusCubit;
  late HiveService _hiveService;
  Timer? _prefsCheckTimer;
  static const Duration refreshInterval = Duration(milliseconds: 1000);

  void checkForCallFromBackgroundIsolate() async {
    // check whether callkit in background isolate already accepted the call.
    // - in android, we use firebase background handler to write user action (accept/decline) into prefs,
    //   after that main app will start and will read from there and accept/decline the call.
    // - in ios, call will be accepted/decline via background service, but we still need to show the
    //   accepted call dialog when app started, so we just check whether there is call ongoing
    await prefs.reload();
    CallkitAction? callkitAction = CallkitAction.fromString(prefs.getString(CacheKeys.callkitAction));
    if (callkitAction == CallkitAction.accept) {
      widget.onAccept();
      prefs.remove(CacheKeys.callkitAction);
    } else if (callkitAction == CallkitAction.decline) {
      widget.onDecline();
      prefs.remove(CacheKeys.callkitAction);
    }
  }

  void removeWhenNotCalling() {
    if (_janusCubit.state is! JanusSipIncomingCallEvent && mounted) {
      pop();
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _janusCubit = BlocProvider.of<JanusCubit>(context);
    _hiveService = sl.get<HiveService>();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _janusCubit.checkCallOnIncomingCallScreenStart();
      checkForCallFromBackgroundIsolate();
      removeWhenNotCalling();
    });

    _prefsCheckTimer?.cancel();
    _prefsCheckTimer = Timer.periodic(refreshInterval, (_) {
      checkForCallFromBackgroundIsolate();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      checkForCallFromBackgroundIsolate();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _prefsCheckTimer?.cancel();
    _prefsCheckTimer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<JanusCubit, JanusState>(
      listener: (context, janusState) {
        if (janusState is JanusSipHangupEvent) {
          popUntilInitial();

          _hiveService.addData<CallRecords>(
            data: CallRecords(
              contactName: widget.callerID!,
              did: widget.caller!,
              duration: '0:00',
              type: CallType.missed,
              datetime: DateTime.now(),
            ),
          );
        } else if (janusState is JanusSipAcceptedEvent) {
          // Close the incoming call dialog since the accepted call screen
          // will be handled centrally in home.dart
          popUntilInitial();

          if (context.mounted) {
            showAcceptedCallDialog(
              context,
              homeCubit: _homeCubit,
              caller: widget.caller,
              callerID: widget.callerID,
            );
          }
        } else if (janusState is JanusSipAcceptErrorEvent) {
          popUntilInitial();
        }
      },
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;

          return AlertDialog(
            title: Text(
              'Incoming call',
              style: TextStyle(
                color: Colors.orange,
                fontSize: context.deviceWidth(0.03),
              ),
            ),
            insetPadding: const EdgeInsets.all(0),
            backgroundColor: const Color.fromARGB(255, 54, 54, 54),
            content: SizedBox(
              width: context.deviceWidth(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Divider(
                    indent: 8.0,
                    endIndent: 8.0,
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  Text(
                    widget.callerID!,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: context.deviceWidth(0.03),
                    ),
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  Text(
                    '${widget.caller}',
                    style: textTheme.displaySmall!.copyWith(color: colorTheme.onBackgroundColor),
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  BlocBuilder<ThemeCubit, ThemeState>(
                    builder: (context, themeState) {
                      final colorTheme = themeState.colorTheme;

                      return Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          RoundShapeInkWell(
                            color: colorTheme.connectedColor,
                            contentWidget: Icon(
                              Icons.call,
                              color: colorTheme.onPrimaryColor,
                              size: context.deviceWidth(0.04),
                            ),
                            onTap: widget.onAccept,
                          ),
                          SizedBox(
                            width: context.deviceWidth(0.1),
                          ),
                          RoundShapeInkWell(
                            checkNetwork: false,
                            color: colorTheme.errorColor,
                            contentWidget: Icon(
                              Icons.close,
                              color: colorTheme.onPrimaryColor,
                              size: context.deviceWidth(0.04),
                            ),
                            onTap: () {
                              popUntilInitial();

                              widget.onDecline();
                            },
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

void showAcceptedCallDialog(
  BuildContext context, {
  required HomeCubit homeCubit,
  String? caller,
  String? callerID,
}) async {
  final HiveService hiveService = sl.get<HiveService>();

  if (caller!.startsWith('sip:')) {
    Match? match = sipNumberRegex.firstMatch(caller);

    if (match != null) {
      String extractedNumber = match.group(1) ?? '';

      caller = extractedNumber;
    }

    // const String start = "sip:";
    // const String end = "@";

    // final startIndex = caller.indexOf(start);
    // final endIndex = caller.indexOf(end, startIndex + start.length);
    // print('extractedNumber: 2$caller');
    // caller = caller.substring(startIndex + start.length, endIndex);
    // print('extractedNumber: 3$caller');
  } else if (callerID!.startsWith('"')) {
    callerID = callerID.substring(1, callerID.length - 1);
  }

  DateTime timeCalled = DateTime.now();

  homeCubit.startStopWatch();

  showDialog(
    barrierDismissible: false,
    context: context,
    builder: (BuildContext context) {
      return AcceptedCallDialog(
        caller: caller,
        callerID: callerID,
        onDecline: (displayTime) {
          hiveService.addData<CallRecords>(
            data: CallRecords(
              contactName: callerID!,
              did: caller!,
              duration: displayTime,
              type: CallType.answered,
              datetime: timeCalled,
            ),
          );
        },
      );
    },
  );
}
