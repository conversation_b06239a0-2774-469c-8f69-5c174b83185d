import 'dart:async';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/misc_constants.dart';
import 'package:ddone/firebase_options.dart';
import 'package:ddone/init.dart';
import 'package:ddone/mixins/api_handler.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/repositories/fcmtoken_repository.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/background_service.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:dio/dio.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await initializeLogger();
  try {
    log.d('Handling firebase background message:${message.toMap()}');

    // setup
    await FirebaseService.initializeApp();
    await mainInit();

    // handling
    BackgroundService.fromBackgroundIsolate = true;
    BackgroundService backgroundService = BackgroundService();
    await backgroundService.handleFirebaseBackgroundMessage(message);
  } catch (e) {
    log.e('Error while handling firebase background message', error: e);
  }
}

class FirebaseService with ApiHandler, PrefsAware {
  // static final singleton pattern
  FirebaseService._internal();
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() {
    return _instance;
  }

  final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  final CallkitService callkitService = sl.get<CallkitService>();

  /// Must be called on app startup before using any other thing
  static Future<void> initializeApp() async {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }
  }

  Future<void> init({
    Function(RemoteMessage)? onMessageOpenedApp, // when user tap on notification
    Function(RemoteMessage)? onMessage, // when message arrived while app in foreground
    Future<void> Function(RemoteMessage)? onBackgroundMessage, // when message arrived while app in background
  }) async {
    // request for permission
    NotificationSettings notificationSettings = await firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    if (notificationSettings.authorizationStatus == AuthorizationStatus.authorized) {
      log.i('FirebaseService - init - Granted permission');
    } else if (notificationSettings.authorizationStatus == AuthorizationStatus.provisional) {
      log.i('FirebaseService - init - Granted provisional permission');
    } else {
      log.i('FirebaseService - init - Declined or not accepted permission');
    }

    // Enable foreground notification for iOS
    await firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    if (isIOS || isMacOS) {
      String? apnsToken = await firebaseMessaging.getAPNSToken();
      log.d('FirebaseService - init - apnsToken:$apnsToken');
    }

    String? fcmtoken = await firebaseMessaging.getToken();
    log.d('FirebaseService - init - fcmToken:$fcmtoken');
    firebaseMessaging.onTokenRefresh.listen((fcmtoken) {
      log.d('FirebaseService - init - onTokenRefresh: $fcmtoken');
    }).onError((e) {
      log.e('FirebaseService - init - onTokenRefresh error', error: e);
    });

    await firebaseMessaging.setAutoInitEnabled(true);

    firebaseMessaging.subscribeToTopic(callTopic);

    // Firebase listen methods

    // 1. When user taps on notification
    RemoteMessage? initialMessage = await firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      log.d('FirebaseService - onMessageOpenedapp - initialMessage:${initialMessage.toMap()}');
      if (onMessageOpenedApp != null) onMessageOpenedApp(initialMessage);
    }
    FirebaseMessaging.onMessageOpenedApp.listen((remoteMessage) {
      log.d('FirebaseService - onMessageOpenedapp - listen:${remoteMessage.toMap()}');
      if (onMessageOpenedApp != null) onMessageOpenedApp(remoteMessage);
    });

    // 2. When app is in foreground
    FirebaseMessaging.onMessage.listen((remoteMessage) {
      log.d('FirebaseService - onMessage - listen:${remoteMessage.toMap()}');
      if (onMessage != null) onMessage(remoteMessage);
    });

    // 3. When app is in background
    if (onBackgroundMessage != null) FirebaseMessaging.onBackgroundMessage(onBackgroundMessage);
  }

  Future<String?> getFcmtoken() async {
    return firebaseMessaging.getToken();
  }

  Future<void> storeFcmToken({
    required String sipNumber,
    required String sipDomain,
    required String pnUrl,
  }) async {
    if (!isMobile) return;

    String? token;
    if (isAndroid || isIOS) {
      token = await getFcmtoken();
    }
    if (token == null) return;

    FcmtokenRepository fcmtokenRepository = sl.get<FcmtokenRepository>();
    await apiErrorHandler(() async {
      String apnsToken = await firebaseMessaging.getAPNSToken() ?? '';
      String voipToken = await callkitService.getDevicePushTokenVoIP() ?? '';
      dynamic body = {
        'accounts': [
          {
            'username': sipNumber,
            'domain': sipDomain,
            'sipaddr': '$sipNumber@$sipDomain',
          }
        ],
        'fcmToken': token,
        'ios': {
          'voice': voipToken,
          'remote': apnsToken,
        }
      };
      log.t('storefcmToken - body:$body');
      fcmtokenRepository = FcmtokenRepository(sl.get<Dio>(), baseUrl: pnUrl);
      await fcmtokenRepository.storeFcmtoken(body);
    }).onError((error, stackTrace) {});

    prefs.setString(CacheKeys.sipHeaderToken, token);
  }

  Future<void> deleteFcmToken({
    required String sipNumber,
    required String sipDomain,
    required String pnUrl,
  }) async {
    if (!isMobile) return;

    String? token;
    if (isAndroid) {
      token = await getFcmtoken();
    } else if (isIOS) {
      token = await callkitService.getDevicePushTokenVoIP();
    }
    if (token == null) return;

    FcmtokenRepository fcmtokenRepository = sl.get<FcmtokenRepository>();
    await apiErrorHandler(() async {
      String apnsToken = await firebaseMessaging.getAPNSToken() ?? '';
      String voipToken = await callkitService.getDevicePushTokenVoIP() ?? '';
      dynamic body = {
        'accounts': [
          {
            'username': sipNumber,
            'domain': sipDomain,
            'sipaddr': '$sipNumber@$sipDomain',
          }
        ],
        'fcmToken': token,
        'ios': {
          'voice': voipToken,
          'remote': apnsToken,
        }
      };
      log.t('deletefcmToken - body:$body');
      fcmtokenRepository = FcmtokenRepository(sl.get<Dio>(), baseUrl: pnUrl);
      await fcmtokenRepository.deleteFcmtoken(body);
    }).onError((error, stackTrace) {});

    prefs.remove(CacheKeys.sipHeaderToken);
  }

  void handleMessage(RemoteMessage remoteMessage) async {
    // randomly wait a while to stager this handleMessage run.
    await randomWait(1, 100);
    await randomWait(1, 100);

    // prevent same mesage get processed more than once.
    String? messageId = remoteMessage.messageId;
    String prefsMessageIdKey = '${CacheKeys.firebaseMessageId}_$messageId';
    await prefs.reload();
    if (prefs.getBool(prefsMessageIdKey) == true) {
      log.t('SKIPPED handleFirebaseMessage for messageId:$messageId');
      return;
    }
    prefs.setBool(prefsMessageIdKey, true, ttlInSeconds: 60);
    log.t('handleFirebaseMessage for messageId:$messageId');

    String? category = remoteMessage.data['category'];
    String caller = remoteMessage.data['caller'] ?? '';
    String callerId = remoteMessage.data['callerId'] ?? '';
    // TODO: fully rely on fcm for message notification
    // String sender = remoteMessage.data['sender'] ?? '';
    // String messageType = remoteMessage.data['messageType'] ?? '';
    // String message = remoteMessage.data['message'] ?? '';
    switch (category) {
      case 'call':
        {
          // ios use APNS PushKit, handled in native iOS swift code, will be triggered
          // regardless of foreground or background. But in android foreground and
          // background handle from different function and have different behavior.
          // Foregroudn is here, background is in firebaseMessagingBackgroundHandler.
          if (isAndroid) {
            callkitService.incomingCall(caller, callerId);
          }
          break;
        }
      case 'miss-call':
        {
          callkitService.endCall();
          callkitService.showMissCall(caller, callerId);
          await Future.wait([
            prefs.setString(CacheKeys.missCall, 'miss call !!!!!'),
            prefs.setString(CacheKeys.missCallName, caller),
            prefs.setString(CacheKeys.missCallId, callerId),
            prefs.setString(CacheKeys.missCallTime, '${DateTime.now()}'),
          ]);
          break;
        }
      case 'end-call':
        {
          callkitService.endCall();
          break;
        }
      default:
        throw Exception('Unhandled category=$category in message');
    }
  }
}
