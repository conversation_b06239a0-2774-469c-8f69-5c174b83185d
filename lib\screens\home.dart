import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ddone/components/button/network_indicator.dart';
import 'package:ddone/components/chat_category/chat_category.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/messaging/receive_message_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/cubit/version_management/version_management_cubit.dart';
import 'package:ddone/cubit/xmpp/connection/xmpp_connection_cubit.dart';
import 'package:ddone/events/app_state_event.dart';
import 'package:ddone/events/background_janus_event.dart';
import 'package:ddone/init.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/screens/chat.dart';
import 'package:ddone/screens/chat_recent.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/cubit/auth/auth_cubit.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/home/<USER>/janus_cubit.dart';
import 'package:ddone/cubit/invitation/invitation_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/background_service.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/elegant_notification_service.dart';
import 'package:ddone/services/fetch_xml_service.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/services/upgrade_version_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/notification_util.dart';
import 'package:ddone/utils/page_view_util.dart';
import 'package:ddone/utils/permission_util.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/utils/orientation_util.dart';
import 'package:ddone/utils/janus/incoming_call.dart';
import 'package:ddone/utils/janus/outgoing_call.dart';
import 'package:event_bus_plus/event_bus_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:logger/logger.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:window_manager/window_manager.dart';
import 'package:tray_manager/tray_manager.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:windows_notification/notification_message.dart';
import 'package:windows_notification/windows_notification.dart';

class Home extends StatefulWidget {
  static const routeName = '/home';

  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home>
    with AutomaticKeepAliveClientMixin<Home>, WindowListener, TrayListener, WidgetsBindingObserver, PrefsAware {
  @override
  bool get wantKeepAlive => true;

  late final String? sipProxy;
  late final String? sipDomain;
  late final String? sipNumber;
  late final String? sipName;
  late final String? sipPwd;

  Menu? _menu;

  late StreamSubscription connectivitySubscription;

  late AuthCubit _authCubit;
  late HomeCubit _homeCubit;
  late JanusCubit _janusCubit;
  late InvitationCubit _invitationCubit;
  late MessagesCubit _messagesCubit;
  late MamListCubit _mamListCubit;
  late BookmarkListCubit _bookmarkListCubit;
  late PresencesCubit _presencesCubit;
  late ChatRecentCubit _chatRecentCubit;
  late ContactsCubit _contactsCubit;
  late LoginCubit _loginCubit;
  late ChatUiCubit _chatUiCubit;
  late GroupUiCubit _groupUiCubit;
  late DeleteBookmarkCubit _deleteBookmarkCubit;
  late InfoCubit _infoCubit;
  late ReceiveMessageCubit _receiveMessageCubit;
  late XmppConnectionCubit _xmppConnectionCubit;
  late VersionManagementCubit _versionManagementCubit;

  late NotificationService _notificationService;
  late FirebaseService _firebaseService;
  late CallkitService _callkitService;
  late WindowsNotification _windowsNotification;
  late Logger log;
  late IEventBus _eventBus;

  Widget buildPageView() {
    return PageView(
      physics: const NeverScrollableScrollPhysics(),
      controller: _homeCubit.pageController,
      onPageChanged: (index) {
        pageChanged(index);
        _homeCubit.jumpToPage(index);
      },
      children: homePageViewDefinition.map((d) => d.widget).toList(),
    );
  }

  void pageChanged(int index) {
    _homeCubit.updateSelectedIndex(index);
  }

  @override
  void onWindowFocus() async {
    log.t('home onWindowFocus janusCubit state ${_janusCubit.state}');
    _janusCubit.maintainConnection();
  }

  @override
  void onWindowBlur() {}

  void handleLocalNotificationAction(NotificationResponse payload) {
    log.t('handleLocalNotificationAction - payload: $payload');
    log.t('handleLocalNotificationAction - payload.notificationResponseType: ${payload.notificationResponseType}');
    log.t('handleLocalNotificationAction - payload.payload: ${payload.payload}');
    log.t('handleLocalNotificationAction - payload.actionId: ${payload.actionId}');
    log.t('handleLocalNotificationAction - payload.input: ${payload.input}');
    log.t('handleLocalNotificationAction - payload.id: ${payload.id}');

    switch (payload.actionId) {
      case notificationAcceptCallAction:
        if (_janusCubit.state is JanusSipIncomingCallEvent) {
          _homeCubit.acceptCall(janusCubit: _janusCubit);
        }

        break;
      case notificationDeclineCallAction:
        if (_janusCubit.state is JanusSipIncomingCallEvent) {
          _homeCubit.declineCall(janusCubit: _janusCubit);
        }

        break;
      default:
        // Handle other actions or payloads
        break;
    }

    if (payload.payload != null) {
      var jsonPayload = json.decode(payload.payload!);

      if (jsonPayload['routeName'] != null) {
        switch (jsonPayload['routeName']) {
          case RecentChat.routeName:
            _homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);
            _chatRecentCubit.setPageIndex(kRecentChatsIndex);
            if (jsonPayload['isGroup']) {
              _infoCubit.getGroupChatHistory(
                receiver: jsonPayload['jid'].split('/').first,
                nick: jsonPayload['jid'].split('/').first,
                name: jsonPayload['jid'].split('/').first,
                loginCubit: _loginCubit,
                mamListCubit: _mamListCubit,
              );
            } else {
              _infoCubit.getChatHistory(
                receiver: jsonPayload['jid'],
                loginCubit: _loginCubit,
                mamListCubit: _mamListCubit,
              );
            }
            if (isMobile && !_notificationService.isChatActive(jsonPayload['jid'])) {
              pushNamed(ChatPage.routeName);
            }
            break;
        }
      }
    }
  }

  void handleWindowsNotificationAction(NotificationCallBackDetails details) {
    final payload = details.message.payload;
    final type = payload['type'];

    switch (type) {
      case 'incoming_call':
        if (details.argrument.toString() == 'action:callAnswer') {
          if (_janusCubit.state is JanusSipIncomingCallEvent) {
            _homeCubit.acceptCall(janusCubit: _janusCubit);
          }
        } else if (details.argrument.toString() == 'action:callDecline') {
          if (_janusCubit.state is JanusSipIncomingCallEvent) {
            _homeCubit.declineCall(janusCubit: _janusCubit);
          }
        }

        break;
      case 'incoming_message':
        _homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);

        if (payload['isGroup']) {
          _infoCubit.getGroupChatHistory(
            receiver: payload['jid'].split('/').first,
            nick: payload['jid'].split('/').first,
            name: payload['jid'].split('/').first,
            loginCubit: _loginCubit,
            mamListCubit: _mamListCubit,
          );
        } else {
          _infoCubit.getChatHistory(
            receiver: payload['jid'],
            loginCubit: _loginCubit,
            mamListCubit: _mamListCubit,
          );
        }
        break;
      default:
    }
  }

  /// TO ASK: only use in loadAndShowDetails? so can ignore??
  void showVersionAlertDialog(Map<String, String> details) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Update!'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('New version of ${env!.appName} is available: ${details['version']}'),
              const SizedBox(height: 4),
              Text('Now your version is ${env!.appVersion}. Please update!'),
              const SizedBox(height: 8),
              const Text('Release Notes:'),
              const SizedBox(height: 2),
              Text('${details['description']}'),
              const SizedBox(height: 8),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                final url = details['downloadPath']!;
                // const url = 'ms-windows-store://pdp/?productid=9P54KFGD0D0W';

                launchUrl(Uri.parse(url));

                pop();
              },
              child: const Text('Update Now'),
            ),
          ],
        );
      },
    );
  }

  /// TO ASK: Intention of this thing???
  void loadAndShowDetails() async {
    try {
      // Fetch XML details
      final details = await fetchAndParseXml('https://purptest.github.io/update_version/appcast.xml');

      // Show details in dialog
      details!['version'] != env!.appVersion ? showVersionAlertDialog(details) : null;
    } catch (e) {
      // Show error dialog
      if (!mounted) return;
      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: const Text('Error'),
            content: Text(e.toString()),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          );
        },
      );
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    _authCubit = BlocProvider.of<AuthCubit>(context);
    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _janusCubit = BlocProvider.of<JanusCubit>(context);
    _invitationCubit = BlocProvider.of<InvitationCubit>(context);
    _messagesCubit = BlocProvider.of<MessagesCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    _bookmarkListCubit = BlocProvider.of<BookmarkListCubit>(context);
    _presencesCubit = BlocProvider.of<PresencesCubit>(context);
    _chatRecentCubit = BlocProvider.of<ChatRecentCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _chatUiCubit = BlocProvider.of<ChatUiCubit>(context);
    _groupUiCubit = BlocProvider.of<GroupUiCubit>(context);
    _deleteBookmarkCubit = BlocProvider.of<DeleteBookmarkCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _receiveMessageCubit = BlocProvider.of<ReceiveMessageCubit>(context);
    _xmppConnectionCubit = BlocProvider.of<XmppConnectionCubit>(context);
    _versionManagementCubit = BlocProvider.of<VersionManagementCubit>(context);

    _notificationService = sl.get<NotificationService>();
    _firebaseService = sl.get<FirebaseService>();
    _callkitService = sl.get<CallkitService>();
    log = sl.get<Logger>();
    _eventBus = sl.get<IEventBus>();

    _notificationService.initializeLocalNotifications(
      onSelectNotification: handleLocalNotificationAction,
      // onBackgroundSelectNotification: handleNotificationAndroidAction,
    );

    _notificationService.requestNotificationPermission();

    if (isWindows) {
      _windowsNotification = sl.get<WindowsNotification>();
      _windowsNotification.initNotificationCallBack(handleWindowsNotificationAction);
    } else if (isMobile) {
      _callkitService.init(
          acceptedCallCallback: (callDirection) {
            if (callDirection != CallDirection.outgoing) {
              log.t('in foreground service accept');
              _callkitAcceptCall();
            }
          },
          declinedCallCallback: (callDirection) {
            if (callDirection != CallDirection.outgoing) {
              log.t('in foreground service decline');
              _callkitDeclineCall();
            }
          },
          endedCallCallback: () {
            _callkitHangupCall();
          },
          missedCallCallback: () {
            _callkitMissedCall();
          },
          iosToggleHoldCallCallback: () {
            _callkitIosHold();
          },
          iosToggleMuteCallCallback: (isMuted) {
            _callkitIosToggleMic(isMuted);
          },
          iosToggleAudioSessionCallback: (isActivate) {});
      _eventBus.on<BackgroundJanusEvent>().listen((event) async {
        // this is meant to hangup call connected through background service.
        log.t('BackgroundJanusEvent event:${event.state}');
        if (await isAppOpen()) {
          if (event.state == (JanusSipUnregisteredEvent).toString()) {
            _janusCubit.emitJanusState(JanusSipUnregisteredEvent);
            await Future.delayed(const Duration(milliseconds: 100));
            await _homeCubit.initRtcClient(janusCubit: _janusCubit, authCubit: _authCubit);
          } else if (event.state == (JanusSipHangupEvent).toString()) {
            popUntilInitial();
          }
        }
      });
      _firebaseService.init(
        onMessageOpenedApp: _handleMessageOnAppOpen,
        onMessage: _firebaseService.handleMessage,
        onBackgroundMessage: firebaseMessagingBackgroundHandler,
      );
    }

    if (isDesktop) {
      windowManager.addListener(this);
      _initWindowManager();

      trayManager.addListener(this);
      setTrayIcon();
    }

    if (isMobile) {
      _homeCubit.updateSelectedIndex(2);
      PermissionUtil.handlePermissionRequest(Permission.microphone, context);
      if (isAndroid) {
        PermissionUtil.handlePermissionRequest(Permission.phone, context);
      }
    }

    _homeCubit.checkForMissCall();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Set device orientations based on device type
      OrientationUtil.setDeviceOrientations(context);

      _versionManagementCubit.checkForUpdates(
        screenWidth: '${MediaQuery.of(context).size.width}',
        screenHeight: '${MediaQuery.of(context).size.height}',
        pixelRatio: '${MediaQuery.of(context).devicePixelRatio}',
      );

      // Request full screen intent permission for Android 14+
      _callkitService.requestCallKitFullIntentPermission(context);

      // Request App Tracking Transparency permission for iOS only
      // AppTrackingTransparencyService.initializeTracking(context);
    });
  }

  Duration _getWaitAppResumeDuration() {
    if (isAndroid) {
      return const Duration(milliseconds: 10000);
    } else {
      return const Duration(milliseconds: 1500);
    }
  }

  Future<void> _callkitAcceptCall() async {
    if (_janusCubit.state is JanusSipIncomingCallEvent ||
        await waitForCondition(() async => _homeCubit.appJustResumed, timeout: _getWaitAppResumeDuration())) {
      // main app get started when user accept call
      log.t('callkitAcceptCall in main app');
      await _homeCubit.acceptCall(janusCubit: _janusCubit);
    } else if (isIOS && !(await isAppOpen())) {
      // main app does not start when user accept call
      // only iOS has such case, in android the main app will be open.
      log.t('callkitAcceptCall in background');
      await mainInit();
      await BackgroundService().acceptCall();
    }
  }

  Future<void> _callkitDeclineCall() async {
    if (_janusCubit.state is JanusSipIncomingCallEvent ||
        await waitForCondition(() async => _homeCubit.appJustResumed, timeout: _getWaitAppResumeDuration())) {
      log.t('callkitDeclineCall in main app');
      await _homeCubit.declineCall(janusCubit: _janusCubit);
    } else if (isIOS && !(await isAppOpen())) {
      log.t('callkitDeclineCall in background');
      await mainInit();
      await BackgroundService().declineCall();
    }
  }

  Future<void> _callkitHangupCall() async {
    if (_janusCubit.state is JanusSipAcceptedEvent) {
      log.t('callkitHangupCall in main app');
      await _homeCubit.hangup(janusCubit: _janusCubit);
    } else if (!(await isAppOpen())) {
      // there will be case where this _callkitHangupCall method get called multiple times,
      // but after the first hangup, it will no longer has JanusSipAcceptedEvent state,
      // so we check only run hangup in background when the app is not open.
      log.t('callkitHangupCall in background');
      await BackgroundService().hangupCall();
    }
  }

  Future<void> _callkitMissedCall() async {
    if (_janusCubit.state is! JanusSipIncomingCallEvent) {
      _janusCubit.dispose();
    }
  }

  Future<void> _callkitIosToggleMic(bool isMuted) async {
    if (_janusCubit.state is JanusSipAcceptedEvent) {
      _homeCubit.toggleMic(janusCubit: _janusCubit);
    } else {
      BackgroundService().iosCallToggleMic(isMuted);
    }
  }

  Future<void> _callkitIosHold() async {
    if (_janusCubit.state is JanusSipAcceptedEvent) {
      _homeCubit.toggleHold(janusCubit: _janusCubit);
    } else {
      BackgroundService().holdCall();
    }
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _janusCubit.remoteVideoRenderer.initialize();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    log.t('home didChangeAppLifecycleState state:$state');
    _eventBus.fire(AppStateEvent(state));
    if (state == AppLifecycleState.resumed) {
      log.t('home didChangeAppLifecycleState janusCubit state ${_janusCubit.state}');
      _janusCubit.maintainConnection();
      _checkCall();
    } else if (state == AppLifecycleState.paused) {
      _janusCubit.disposeWhenIsMobileAndNoCalls();
    }
  }

  void _checkCall() async {
    // wait a while such that it janus state can get updated when the app start to avoid janus state mess up
    await Future.delayed(const Duration(milliseconds: 1000));
    await _janusCubit.checkCallOnHomeScreenStart();
  }

  @override
  void dispose() {
    connectivitySubscription.cancel();
    windowManager.removeListener(this);
    trayManager.removeListener(this);

    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  void _handleMessageOnAppOpen(RemoteMessage message) {
    if (message.data['type'] == 'chat') {}
    debugPrint('_handleMessage: ${message.data}');
    final RemoteNotification? notification = message.notification;
    if (notification != null) {
      debugPrint('******** Received a message on app open! ********');
      debugPrint('******** Notification Title: ${notification.title} ********');
      debugPrint('******** Notification Body: ${notification.body} ********');
    }
  }

  void _initWindowManager() async {
    await windowManager.setPreventClose(true);
  }

  void setTrayIcon() async {
    await trayManager.setIcon(isDesktop ? '$imagePathPrefix/app_icon.ico' : '$imagePathPrefix/dot_dash_logo.png');

    _menu ??= Menu(items: [
      MenuItem(
        label: 'Show App',
        onClick: (menuItem) {
          windowManager.show();
        },
      ),
      MenuItem(
        label: 'Exit',
        onClick: (menuItem) async {
          trayManager.destroy();

          windowManager.destroy();

          if (_loginCubit.state is LoginAuthenticated) {
            final loginAuthenticatedState = _loginCubit.state as LoginAuthenticated;
            final presenceUnsubscribe =
                loginAuthenticatedState.connection.getManagerById<PresenceManager>(presenceManager)!;
            await presenceUnsubscribe.sendUnavailablePresence();

            loginAuthenticatedState.connection.disconnect();
          }
        },
      )
    ]);

    await trayManager.setContextMenu(_menu!);
  }

  @override
  void onTrayIconMouseDown() {
    windowManager.show();
  }

  @override
  void onTrayIconRightMouseDown() {
    trayManager.popUpContextMenu();
  }

  @override
  void onWindowClose() async {
    showConfirmDialog(
      context: context,
      title: 'Do you want to exit?',
      rightButtonText: 'Minimize',
      onPressedRightButton: windowManager.hide,
      leftButtonText: 'Exit',
      onPressedLeftButton: () async {
        trayManager.destroy();

        windowManager.destroy();

        if (_loginCubit.state is LoginAuthenticated) {
          final loginAuthenticatedState = _loginCubit.state as LoginAuthenticated;
          final presenceUnsubscribe =
              loginAuthenticatedState.connection.getManagerById<PresenceManager>(presenceManager)!;
          await presenceUnsubscribe.sendUnavailablePresence();

          loginAuthenticatedState.connection.disconnect();
        }
      },
    );
  }

  void _showVersionUpdateDialog(BuildContext context,
      {required bool forceUpdate,
      required String currentVersion,
      required String latestVersion,
      required String updateUrl}) {
    showDialog(
      context: context,
      barrierDismissible: false, // User must interact with the dialog
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Update Available!'),
          content: Text(
            forceUpdate
                ? 'A compulsory update is available. Please update to continue using the app.\nCurrent version: $currentVersion\nLatest version: $latestVersion'
                : 'A new version is available. Would you like to update?\nCurrent version: $currentVersion\nLatest version: $latestVersion',
          ),
          actions: <Widget>[
            if (!forceUpdate)
              TextButton(
                child: const Text('Later'),
                onPressed: () {
                  pop();
                },
              ),
            TextButton(
              child: const Text('Update Now'),
              onPressed: () {
                _launchStoreUrl(updateUrl);
                if (!forceUpdate) pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(BuildContext context, String errorMessage) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Version Check Failed'),
          content: Text('An error occurred: $errorMessage'),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _launchStoreUrl(String storeUrl) async {
    final Uri url = Uri.parse(storeUrl);
    if (await canLaunchUrl(url)) {
      bool launched = await launchUrl(url, mode: LaunchMode.externalApplication);
      if (!launched) {
        log.d('Could not launch $url in external app');
      }
    } else {
      log.e('Cannot launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return MultiBlocListener(
      listeners: [
        BlocListener<ReceiveMessageCubit, ReceiveMessageState>(listener: (context, receveMsgState) {
          if (receveMsgState is ReceiveMessageUpdated) {
            if (Platform.isWindows) {
              ElegantNotificationService elegantNotificationService = ElegantNotificationService();

              elegantNotificationService.showStackNotification(
                context,
                title: receveMsgState.name,
                desc: receveMsgState.message,
                gestureTapCallback: () {
                  _homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);

                  if (receveMsgState.isGroup) {
                    _infoCubit.getGroupChatHistory(
                      receiver: receveMsgState.jid.split('/').first,
                      nick: receveMsgState.jid.split('/').first,
                      name: receveMsgState.jid.split('/').first,
                      loginCubit: _loginCubit,
                      mamListCubit: _mamListCubit,
                    );
                  } else {
                    _infoCubit.getChatHistory(
                      receiver: receveMsgState.jid,
                      loginCubit: _loginCubit,
                      mamListCubit: _mamListCubit,
                    );
                  }
                },
              );
            }
          }
        }),
        BlocListener<NetworkCubit, NetworkState>(
          listener: (context, networkState) {
            if (networkState is NetworkConnected) {
              _authCubit.autoLogin(
                homeCubit: _homeCubit,
                janusCubit: _janusCubit,
                authCubit: _authCubit,
                contactsCubit: _contactsCubit,
                loginCubit: _loginCubit,
                invitationCubit: _invitationCubit,
                messagesCubit: _messagesCubit,
                mamListCubit: _mamListCubit,
                bookmarkListCubit: _bookmarkListCubit,
                presencesCubit: _presencesCubit,
                chatRecentCubit: _chatRecentCubit,
                chatUiCubit: _chatUiCubit,
                groupUiCubit: _groupUiCubit,
                deleteBookmarkCubit: _deleteBookmarkCubit,
                receiveMessageCubit: _receiveMessageCubit,
                xmppConnectionCubit: _xmppConnectionCubit,
                versionManagementCubit: _versionManagementCubit,
              );
            } else if (networkState is NetworkReconnected) {
              showSnackBarWithText(
                context,
                'Connected to network',
              );
              _authCubit.autoLogin(
                homeCubit: _homeCubit,
                janusCubit: _janusCubit,
                authCubit: _authCubit,
                contactsCubit: _contactsCubit,
                loginCubit: _loginCubit,
                invitationCubit: _invitationCubit,
                messagesCubit: _messagesCubit,
                mamListCubit: _mamListCubit,
                bookmarkListCubit: _bookmarkListCubit,
                presencesCubit: _presencesCubit,
                chatRecentCubit: _chatRecentCubit,
                chatUiCubit: _chatUiCubit,
                groupUiCubit: _groupUiCubit,
                deleteBookmarkCubit: _deleteBookmarkCubit,
                receiveMessageCubit: _receiveMessageCubit,
                xmppConnectionCubit: _xmppConnectionCubit,
                versionManagementCubit: _versionManagementCubit,
              );
            } else if (networkState is NetworkDisconnected) {
              showSnackBarWithText(
                context,
                'Disconnected to network',
              );
            }
          },
        ),
        BlocListener<JanusCubit, JanusState>(
          listener: (context, janusState) {
            if (janusState is JanusSipIncomingCallEvent) {
              _homeCubit.playIncomingRingtone();

              if (isWindows) {
                windowManager.show();
              } else {
                if (isDesktop) {
                  _notificationService.showLocalNotification(
                    title: janusState.callerID!,
                    desc: 'Incoming Call',
                    payload: '',
                    // androidNotificaitonAction: androidIncomingCallTemplate,
                    // darwinNotificationDetails: iOSMacosIncomingCallTemplete,
                    windowNotificationMessage: windowsNotificationMessage(
                      id: 'notificationid_1',
                      group: 'incoming_call_group',
                      launch: 'callLaunchArg',
                      payload: {'type': 'incoming_call'},
                    ),
                    windowsNotificationTemplate: twoButtonsNotificationTemplateForWindows(
                      title: janusState.callerID!,
                      desc: 'Incoming Call',
                      leftButtonText: 'Decline',
                      rightButtonText: 'Answer',
                      leftButtonArg: 'callDecline',
                      rightButtonArg: 'callAnswer',
                      scenario: 'incomingCall',
                      launch: 'callLaunchArg',
                    ),
                  );
                }
              }

              showDialog(
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) {
                  return IncomingCallDialog(
                    caller: janusState.caller,
                    callerID: janusState.callerID,
                    onAccept: () {
                      if (isIOS && _callkitService.isRinging) {
                        // in IOS when we accept call through callkit, it will start audio session
                        // if we don't start it through _callkitAcceptCall method, then audio session
                        // will mess up. So we ping this to let _callkitAcceptCall run.
                        _callkitService.connectedCall();
                      } else {
                        _homeCubit.acceptCall(janusCubit: _janusCubit);
                      }
                    },
                    onDecline: () => _homeCubit.declineCall(janusCubit: _janusCubit),
                  );
                },
              );
            } else if (janusState is JanusSipCallingEvent) {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) {
                  return OutGoingCallDialog();
                },
              );
            } else if (janusState is JanusSipAcceptedEvent) {
              // Handle accepted call state directly - this allows the app to show
              // the accepted call screen even when starting directly in VoipSipAccepted state
              // Use a small delay to ensure any existing dialogs are properly closed
              // before showing the accepted call dialog
              Future.delayed(const Duration(milliseconds: 200), () {
                if (context.mounted) {
                  // Start the stopwatch for call duration tracking
                  _homeCubit.startStopWatch();
                  showAcceptedCallDialog(
                    context,
                    homeCubit: _homeCubit,
                    caller: janusState.caller ?? janusState.callTo,
                    callerID: janusState.callerID ?? janusState.callerID,
                  );
                }
              });
            } else if (janusState is JanusSipHangupEvent) {
              _homeCubit.stopAndResetRingtonePlayer();
            }
          },
        ),
        BlocListener<AuthCubit, AuthState>(
          listener: (context, authState) {
            if (authState is AuthAutoLoginFail) {
              EasyLoadingService().showErrorWithText('Please log in again');
            }
          },
        ),
        BlocListener<VersionManagementCubit, VersionManagementState>(
          listener: (context, versionState) {
            if (versionState is VersionOutdated) {
              _showVersionUpdateDialog(
                context,
                forceUpdate: versionState.forceUpdate,
                currentVersion: versionState.curerntVersion,
                latestVersion: versionState.latestVersion,
                updateUrl: versionState.updateUrl,
              );
            } else if (versionState is VersionCheckFailed) {
              _showErrorDialog(context, versionState.error);
            }
          },
        ),
      ],
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;
          const microsoftStoreUrl = 'https://www.microsoft.com/store/r/9P54KFGD0D0W';
          final latestVersion = UpgrateVersionService().fetchMicrosoftStoreVersion(microsoftStoreUrl);
          log.d('Latest Version Microsoft : ${latestVersion.toString()}');

          return SafeArea(
            child: Scaffold(
              backgroundColor: colorTheme.backgroundColor,
              body: isDesktop
                  ? Row(
                      children: <Widget>[
                        BlocBuilder<HomeCubit, HomeState>(
                          builder: (context, homeState) {
                            return NavigationRail(
                              backgroundColor: Colors.grey.shade800,
                              selectedIndex: homeState.navSelectedIndex,
                              groupAlignment: 0.0,
                              unselectedIconTheme:
                                  IconThemeData(color: colorTheme.onPrimaryColor.withOpacity(opacityHigh)),
                              unselectedLabelTextStyle: textTheme.bodyMedium!.copyWith(
                                color: colorTheme.onPrimaryColor.withOpacity(opacityHigh),
                              ),
                              selectedLabelTextStyle: textTheme.bodyMedium!.copyWith(color: colorTheme.primaryColor),
                              onDestinationSelected: (int index) {
                                _homeCubit.bottomTapped(index);
                              },
                              labelType: NavigationRailLabelType.all,
                              leading: Column(
                                children: [
                                  const NetworkIndicatorButton(),
                                  SizedBox(
                                    height: context.deviceHeight(0.17),
                                  )
                                ],
                              ),
                              trailing: Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 8.0),
                                  child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: InkWell(
                                        splashFactory: NoSplash.splashFactory,
                                        onTap: () async {
                                          final Uri url = Uri.parse(env!.contactUsUrl);

                                          if (!await launchUrl(url)) {
                                            throw Exception('Could not launch $url');
                                          }
                                        },
                                        child: Text(
                                          'Support',
                                          style: textTheme.bodyMedium!.copyWith(color: colorTheme.primaryColor),
                                        ),
                                      )),
                                ),
                              ),
                              destinations: const [
                                NavigationRailDestination(
                                  icon: Icon(Icons.history),
                                  label: Text('History'),
                                ),
                                NavigationRailDestination(
                                  icon: Icon(Icons.dialpad_outlined),
                                  label: Text('Main'),
                                ),
                                NavigationRailDestination(
                                  icon: Icon(Icons.chat),
                                  label: Text('Chat'),
                                ),
                                NavigationRailDestination(
                                  icon: Icon(Icons.groups),
                                  label: Text('DDMeet'),
                                ),
                                NavigationRailDestination(
                                  icon: Icon(Icons.person),
                                  label: Text('Me'),
                                ),
                              ],
                            );
                          },
                        ),
                        Expanded(
                          child: buildPageView(),
                        ),
                      ],
                    )
                  : buildPageView(),
              bottomNavigationBar: isDesktop
                  ? null
                  : BlocBuilder<HomeCubit, HomeState>(
                      builder: (context, homeState) {
                        return BottomNavigationBar(
                          type: BottomNavigationBarType.fixed,
                          iconSize: iconSizeLarge,
                          currentIndex: homeState.navSelectedIndex,
                          onTap: (index) {
                            _homeCubit.bottomTapped(index);
                          },
                          items: const [
                            BottomNavigationBarItem(
                              icon: Icon(Icons.history),
                              label: 'History',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.perm_contact_cal_sharp),
                              label: 'Contacts',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.dialpad_outlined),
                              label: 'Dial',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.chat),
                              label: 'Chat',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.groups),
                              label: 'DDMeet',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.person),
                              label: 'Me',
                            ),
                          ],
                          // unselectedItemColor: Colors.grey,
                          selectedItemColor: colorTheme.onBackgroundColor,
                          showUnselectedLabels: true,
                        );
                      },
                    ),
            ),
          );
        },
      ),
    );
  }
}

class BottomNavBarItem extends StatelessWidget {
  final IconData iconData;
  final String text;
  final bool isSelected;
  final GestureTapCallback gestureTapCallback;

  const BottomNavBarItem({
    required this.iconData,
    required this.text,
    required this.isSelected,
    required this.gestureTapCallback,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return InkWell(
          onTap: gestureTapCallback,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                iconData,
                size: iconSizeLarge,
                color: isSelected ? colorTheme.onBackgroundColor : colorTheme.backgroundColor,
              ),
              Text(
                text,
                style: textTheme.labelLarge!
                    .copyWith(color: isSelected ? colorTheme.onBackgroundColor : colorTheme.backgroundColor),
              ),
            ],
          ),
        );
      },
    );
  }
}
